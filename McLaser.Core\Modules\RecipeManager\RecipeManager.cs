﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;

namespace McLaser.Core.Modules.RecipeManager
{
    [Export(typeof(IRecipeManager))]
    public class RecipeManager : IRecipeManager
    {
        private readonly IRecipeManagerOption _option;
        public IRecipe Current { get; set; }
        public Action<IRecipeItem, IRecipeItem> CurrentChanged { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }


        [ImportingConstructor]
        public RecipeManager(IRecipeManagerOption option)
        {
            _option = option;
        }

        public void Add(IRecipeItem project)
        {
            throw new NotImplementedException();
        }

        public bool Create(out IRecipe recipe)
        {
            try
            {
                recipe = new Recipe();
                return true;
            }
            catch (Exception ex)
            {

            }
            recipe = null;
            return false;
        }

        public bool Load(string recipeName)
        {
            try
            {
                if (Current == null) return false;
                Current.Name = recipeName; 
                Current?.Load();
                return true;
                //string filePath = PathDefine.FolderRecipe + $"{recipeName}";
                ////var recipe = JsonHelper.DeserializeObject<Recipe>(filePath);

                ////获取一下系统RecipeItems
                //var items = IoC.GetAll<IRecipeItem>();
                //foreach (var item in items)
                //{
                //    item.Load(filePath);
                //}


                //var diffItems = items.Where(a => !recipe.RecipeItems.Any(b => b.GetType() == a.GetType()));
                //var list = recipe.RecipeItems.ToList();
                //if (diffItems != null && diffItems.Count() > 0)
                //{
                //    foreach (var item in diffItems)
                //    {
                //        list.Add(item);
                //        // recipe.RecipeItems.Append(item);
                //    }
                //    recipe.RecipeItems = list;
                //}
                //return recipe;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public bool Save()
        {
            Current.Save();
            return true;
        }

        public void Delete(Func<IRecipeItem, bool> func)
        {
            throw new NotImplementedException();
        }

        public IEnumerable<IRecipeItem> Where(Func<IRecipeItem, bool> func = null)
        {
            throw new NotImplementedException();
        }


    }
}
